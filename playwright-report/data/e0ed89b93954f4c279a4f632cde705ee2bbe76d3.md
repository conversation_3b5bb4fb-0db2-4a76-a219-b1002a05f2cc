# Test info

- Name: Organization member invitation with email mocking >> should handle email service failures gracefully
- Location: /Users/<USER>/Projects/project-controls/e2e/organizations/invite-member-with-email-mock.test.ts:91:2

# Error details

```
TimeoutError: locator.click: Timeout 10000ms exceeded.
Call log:
  - waiting for locator('[data-slot="select-item"][data-value="member"]')

    at /Users/<USER>/Projects/project-controls/e2e/organizations/invite-member-with-email-mock.test.ts:102:72
```

# Page snapshot

```yaml
- region "Notifications alt+T"
- link:
  - /url: /
  - img
- separator
- list:
  - listitem:
    - link "Clients":
      - /url: /org/Test Org 1748425045630/clients
      - button "Clients":
        - img
        - text: Clients
    - button:
      - img
  - listitem:
    - link "WBS Libraries":
      - /url: /wbs-libraries
      - button "WBS Libraries":
        - img
        - text: WBS Libraries
    - button:
      - img
  - listitem:
    - link "Contractors":
      - /url: /contractors
      - button "Contractors":
        - img
        - text: Contractors
    - button:
      - img
- separator
- list:
  - listitem:
    - button "U"
- button "Toggle Sidebar"
- main:
  - button "Toggle Sidebar":
    - img
    - text: Toggle Sidebar
  - separator
  - navigation "breadcrumb":
    - list:
      - listitem:
        - link "Test Org 1748425045630":
          - /url: /org/Test Org 1748425045630
      - listitem:
        - link "Members":
          - /url: /org/Test Org 1748425045630/members
  - heading "Invite Member" [level=1]
  - text: Email *
  - textbox "Email *": <EMAIL>
  - text: Role *
  - button "Role *":
    - text: Member
    - img
  - button "Invite"
```

# Test source

```ts
   2 | import { emailMock } from '../setup/email-mock';
   3 |
   4 | test.describe('Organization member invitation with email mocking', () => {
   5 | 	const testOrgName = `Test Org ${Date.now()}`;
   6 | 	const testEmail = `test-${Date.now()}@example.com`;
   7 | 	const testPassword = 'TestPassword123';
   8 |
   9 | 	test.beforeAll(async ({ browser }) => {
   10 | 		// Create a test user and organization first
   11 | 		const page = await browser.newPage();
   12 |
   13 | 		// Setup email mocking for this page
   14 | 		await emailMock.setupEmailMocking(page);
   15 |
   16 | 		await page.goto('/auth/signup');
   17 | 		await page.fill('input[name="email"]', testEmail);
   18 | 		await page.fill('input[name="password"]', testPassword);
   19 | 		await page.click('button[type="submit"]');
   20 |
   21 | 		// Wait for signup success message
   22 | 		await expect(page.locator('text=User created successfully')).toBeVisible({ timeout: 10000 });
   23 |
   24 | 		// Sign in
   25 | 		await page.goto('/auth/signin');
   26 | 		await page.fill('input[name="email"]', testEmail);
   27 | 		await page.fill('input[name="password"]', testPassword);
   28 | 		await page.click('button[type="submit"]');
   29 |
   30 | 		// Should be redirected to org creation since user has no orgs
   31 | 		await page.waitForURL(/\/org\/new/, { timeout: 10000 });
   32 |
   33 | 		// Create organization
   34 | 		await page.fill('input[name="name"]', testOrgName);
   35 | 		await page.click('button[type="submit"]');
   36 |
   37 | 		// Wait for redirect to clients page
   38 | 		await page.waitForURL(/\/org\/.*\/clients/, { timeout: 10000 });
   39 | 		await page.close();
   40 | 	});
   41 |
   42 | 	test.beforeEach(async ({ page }) => {
   43 | 		// Setup email mocking for each test
   44 | 		await emailMock.setupEmailMocking(page);
   45 |
   46 | 		// Clear previous email calls
   47 | 		emailMock.clearEmailCalls();
   48 |
   49 | 		// Sign in
   50 | 		await page.goto('/auth/signin');
   51 | 		await page.fill('input[name="email"]', testEmail);
   52 | 		await page.fill('input[name="password"]', testPassword);
   53 | 		await page.click('button[type="submit"]');
   54 |
   55 | 		// Navigate to invite page
   56 | 		await page.goto(`/org/${encodeURIComponent(testOrgName)}/invite`);
   57 | 	});
   58 |
   59 | 	test('should successfully invite a new member and capture email', async ({ page }) => {
   60 | 		const inviteEmail = '<EMAIL>';
   61 |
   62 | 		// Fill the form with valid data
   63 | 		await page.fill('input[name="email"]', inviteEmail);
   64 |
   65 | 		// Submit the form
   66 | 		const submitButton = page.locator('button[type="submit"]');
   67 | 		await expect(submitButton).toBeVisible();
   68 | 		await submitButton.click();
   69 |
   70 | 		// Wait for form submission to complete
   71 | 		await page.waitForLoadState('networkidle');
   72 |
   73 | 		// The most important part: verify email was captured by the mock
   74 | 		expect(emailMock.getEmailCallCount()).toBe(1);
   75 |
   76 | 		const emailCall = emailMock.getLastEmailCall();
   77 | 		expect(emailCall).not.toBeNull();
   78 | 		expect(emailCall!.to).toBe(inviteEmail);
   79 | 		expect(emailCall!.subject).toContain('invited to join');
   80 | 		expect(emailCall!.html).toContain('You have been invited');
   81 | 		expect(emailCall!.html).toContain('/auth/invite/');
   82 |
   83 | 		// Alternative assertion method using the built-in assert function
   84 | 		emailMock.assertEmailSent({
   85 | 			to: inviteEmail,
   86 | 			subject: /invited to join.*organization/i,
   87 | 			containsHtml: 'You have been invited',
   88 | 		});
   89 | 	});
   90 |
   91 | 	test('should handle email service failures gracefully', async ({ page }) => {
   92 | 		// Configure the mock to simulate email failures
   93 | 		emailMock.setEmailFailure(true);
   94 |
   95 | 		const inviteEmail = '<EMAIL>';
   96 |
   97 | 		// Fill the form with valid data
   98 | 		await page.fill('input[name="email"]', inviteEmail);
   99 |
  100 | 		// Select role using the Select component - wait for dropdown to appear
  101 | 		await page.locator('[data-slot="select-trigger"]').click();
> 102 | 		await page.locator('[data-slot="select-item"][data-value="member"]').click();
      | 		                                                                     ^ TimeoutError: locator.click: Timeout 10000ms exceeded.
  103 |
  104 | 		// Submit the form
  105 | 		const submitButton = page.locator('button[type="submit"]');
  106 | 		await expect(submitButton).toBeVisible();
  107 | 		await submitButton.click();
  108 |
  109 | 		// Wait for error message
  110 | 		await expect(page.locator('text=/failed to send invitation email/i')).toBeVisible({
  111 | 			timeout: 10000,
  112 | 		});
  113 |
  114 | 		// Verify that the email was still captured (even though it "failed")
  115 | 		expect(emailMock.getEmailCallCount()).toBe(1);
  116 | 		expect(emailMock.wasEmailSentTo(inviteEmail)).toBe(true);
  117 |
  118 | 		// Reset email failure for other tests
  119 | 		emailMock.setEmailFailure(false);
  120 | 	});
  121 |
  122 | 	test('should allow admin role selection and capture appropriate email', async ({ page }) => {
  123 | 		const inviteEmail = '<EMAIL>';
  124 |
  125 | 		// Fill the form with valid data
  126 | 		await page.fill('input[name="email"]', inviteEmail);
  127 |
  128 | 		// Select admin role using the Select component - wait for dropdown to appear
  129 | 		await page.locator('[data-slot="select-trigger"]').click();
  130 | 		await page.waitForSelector('[data-slot="select-content"]', { state: 'visible' });
  131 | 		await page.locator('[data-slot="select-item"][data-value="admin"]').click();
  132 |
  133 | 		// Submit the form
  134 | 		const submitButton = page.locator('button[type="submit"]');
  135 | 		await expect(submitButton).toBeVisible();
  136 | 		await submitButton.click();
  137 |
  138 | 		// Wait for success message
  139 | 		await expect(page.locator('text=/invitation sent/i')).toBeVisible({ timeout: 10000 });
  140 |
  141 | 		// Verify email was captured
  142 | 		const emailCall = emailMock.getLastEmailCall();
  143 | 		expect(emailCall).not.toBeNull();
  144 | 		expect(emailCall!.to).toBe(inviteEmail);
  145 |
  146 | 		// Verify the email content is appropriate for organization invitation
  147 | 		expect(emailCall!.subject).toContain('invited to join the organization');
  148 | 	});
  149 |
  150 | 	test('should capture multiple email invitations', async ({ page }) => {
  151 | 		const emails = ['<EMAIL>', '<EMAIL>'];
  152 |
  153 | 		for (const email of emails) {
  154 | 			// Fill the form
  155 | 			await page.fill('input[name="email"]', email);
  156 |
  157 | 			// Select role - wait for dropdown to appear
  158 | 			await page.locator('[data-slot="select-trigger"]').click();
  159 | 			await page.waitForSelector('[data-slot="select-content"]', { state: 'visible' });
  160 | 			await page.locator('[data-slot="select-item"][data-value="member"]').click();
  161 |
  162 | 			// Submit
  163 | 			const submitButton = page.locator('button[type="submit"]');
  164 | 			await submitButton.click();
  165 |
  166 | 			// Wait for success
  167 | 			await expect(page.locator('text=/invitation sent/i')).toBeVisible({ timeout: 10000 });
  168 |
  169 | 			// Clear the form for next iteration
  170 | 			await page.fill('input[name="email"]', '');
  171 | 		}
  172 |
  173 | 		// Verify both emails were captured
  174 | 		expect(emailMock.getEmailCallCount()).toBe(2);
  175 |
  176 | 		for (const email of emails) {
  177 | 			expect(emailMock.wasEmailSentTo(email)).toBe(true);
  178 | 		}
  179 |
  180 | 		// Verify we can find emails by recipient
  181 | 		const user1Emails = emailMock.getEmailCallsTo('<EMAIL>');
  182 | 		expect(user1Emails).toHaveLength(1);
  183 |
  184 | 		const user2Emails = emailMock.getEmailCallsTo('<EMAIL>');
  185 | 		expect(user2Emails).toHaveLength(1);
  186 | 	});
  187 |
  188 | 	test('should demonstrate async email waiting', async ({ page }) => {
  189 | 		const inviteEmail = '<EMAIL>';
  190 |
  191 | 		// Fill and submit form
  192 | 		await page.fill('input[name="email"]', inviteEmail);
  193 | 		await page.locator('[data-slot="select-trigger"]').click();
  194 | 		await page.waitForSelector('[data-slot="select-content"]', { state: 'visible' });
  195 | 		await page.locator('[data-slot="select-item"][data-value="member"]').click();
  196 |
  197 | 		const submitButton = page.locator('button[type="submit"]');
  198 | 		await submitButton.click();
  199 |
  200 | 		// Use the async email waiting feature
  201 | 		const emailCall = await emailMock.waitForEmail(
  202 | 			(call) => call.to === inviteEmail,
```