import { Page, Route } from '@playwright/test';

/**
 * Email mock data structure for capturing email calls
 */
export interface MockEmailCall {
	to: string;
	subject: string;
	text?: string;
	html?: string;
	timestamp: number;
}

/**
 * Email mock service for e2e tests
 * Provides utilities to mock email sending and capture email calls for assertions
 */
export class EmailMockService {
	private emailCalls: MockEmailCall[] = [];
	private shouldFailEmails = false;

	/**
	 * Setup email mocking for a Playwright page
	 * Intercepts calls to /api/invites and form submissions that trigger email sending
	 */
	async setupEmailMocking(page: Page): Promise<void> {
		// Mock the /api/invites endpoint which uses sendEmail (for direct API calls)
		await page.route('/api/invites', async (route: Route) => {
			const request = route.request();

			if (request.method() === 'POST') {
				try {
					const body = await request.postDataJSON();

					// Simulate email sending by capturing the call
					const emailCall: MockEmailCall = {
						to: body.inviteeEmail,
						subject: `You have been invited to join ${this.getJoinWhat(body)} on Cost Atlas`,
						html: `<p>You have been invited to join ${this.getJoinWhat(body)}. Click <a href="http://localhost:5173/auth/invite/mock-token">here</a> to accept or decline the invitation.</p>`,
						timestamp: Date.now(),
					};

					this.emailCalls.push(emailCall);

					// Return success or failure based on mock configuration
					if (this.shouldFailEmails) {
						await route.fulfill({
							status: 500,
							contentType: 'application/json',
							body: JSON.stringify({ error: 'Mock email service failure' }),
						});
					} else {
						await route.fulfill({
							status: 200,
							contentType: 'application/json',
							body: JSON.stringify({ success: true }),
						});
					}
				} catch (error) {
					console.error('Error in email mock:', error);
					await route.fulfill({
						status: 500,
						contentType: 'application/json',
						body: JSON.stringify({ error: 'Mock processing error' }),
					});
				}
			} else {
				// For non-POST requests, continue normally
				await route.continue();
			}
		});

		// Also intercept form submissions to invite pages (since they make server-side API calls)
		await page.route('**/org/*/invite', async (route: Route) => {
			const request = route.request();

			if (request.method() === 'POST') {
				try {
					// Parse form data
					const formData = request.postData();
					const params = new URLSearchParams(formData || '');
					const email = params.get('email');

					if (email) {
						// Simulate email sending by capturing the call
						const emailCall: MockEmailCall = {
							to: email,
							subject: `You have been invited to join the organization on Cost Atlas`,
							html: `<p>You have been invited to join the organization. Click <a href="http://localhost:5173/auth/invite/mock-token">here</a> to accept or decline the invitation.</p>`,
							timestamp: Date.now(),
						};

						this.emailCalls.push(emailCall);
					}

					// Return success or failure based on mock configuration
					if (this.shouldFailEmails) {
						// Return a form response with error message
						await route.fulfill({
							status: 302,
							headers: {
								Location: route.request().url(),
								'Set-Cookie':
									'flash=%7B%22type%22%3A%22error%22%2C%22message%22%3A%22Failed%20to%20send%20invitation%20email.%20Please%20try%20again.%22%7D; Path=/; HttpOnly',
							},
						});
					} else {
						// Return a form response with success message
						await route.fulfill({
							status: 302,
							headers: {
								Location: route.request().url(),
								'Set-Cookie':
									'flash=%7B%22type%22%3A%22success%22%2C%22message%22%3A%22Invitation%20sent%22%7D; Path=/; HttpOnly',
							},
						});
					}
				} catch (error) {
					console.error('Error in form mock:', error);
					await route.continue();
				}
			} else {
				// For non-POST requests, continue normally
				await route.continue();
			}
		});
	}

	/**
	 * Get the "join what" text based on resource type
	 */
	private getJoinWhat(body: { resourceType?: string }): string {
		switch (body.resourceType) {
			case 'organization':
				return 'the organization';
			case 'client':
				return 'the client';
			case 'project':
				return 'the project';
			default:
				return 'the resource';
		}
	}

	/**
	 * Configure the mock to simulate email failures
	 */
	setEmailFailure(shouldFail: boolean): void {
		this.shouldFailEmails = shouldFail;
	}

	/**
	 * Get all captured email calls
	 */
	getEmailCalls(): MockEmailCall[] {
		return [...this.emailCalls];
	}

	/**
	 * Get the most recent email call
	 */
	getLastEmailCall(): MockEmailCall | null {
		return this.emailCalls.length > 0 ? this.emailCalls[this.emailCalls.length - 1] : null;
	}

	/**
	 * Find email calls by recipient
	 */
	getEmailCallsTo(email: string): MockEmailCall[] {
		return this.emailCalls.filter((call) => call.to === email);
	}

	/**
	 * Find email calls by subject pattern
	 */
	getEmailCallsBySubject(subjectPattern: string | RegExp): MockEmailCall[] {
		const pattern =
			typeof subjectPattern === 'string' ? new RegExp(subjectPattern, 'i') : subjectPattern;
		return this.emailCalls.filter((call) => pattern.test(call.subject));
	}

	/**
	 * Clear all captured email calls
	 */
	clearEmailCalls(): void {
		this.emailCalls = [];
	}

	/**
	 * Get count of email calls
	 */
	getEmailCallCount(): number {
		return this.emailCalls.length;
	}

	/**
	 * Check if any emails were sent to a specific recipient
	 */
	wasEmailSentTo(email: string): boolean {
		return this.emailCalls.some((call) => call.to === email);
	}

	/**
	 * Check if any emails were sent with a specific subject pattern
	 */
	wasEmailSentWithSubject(subjectPattern: string | RegExp): boolean {
		const pattern =
			typeof subjectPattern === 'string' ? new RegExp(subjectPattern, 'i') : subjectPattern;
		return this.emailCalls.some((call) => pattern.test(call.subject));
	}

	/**
	 * Wait for an email to be sent (useful for async operations)
	 */
	async waitForEmail(
		predicate: (call: MockEmailCall) => boolean,
		timeout: number = 5000,
	): Promise<MockEmailCall | null> {
		const startTime = Date.now();

		while (Date.now() - startTime < timeout) {
			const matchingCall = this.emailCalls.find(predicate);
			if (matchingCall) {
				return matchingCall;
			}

			// Wait a bit before checking again
			await new Promise((resolve) => setTimeout(resolve, 100));
		}

		return null;
	}

	/**
	 * Assert that an email was sent with specific criteria
	 * Throws an error if the assertion fails
	 */
	assertEmailSent(criteria: {
		to?: string;
		subject?: string | RegExp;
		containsText?: string;
		containsHtml?: string;
	}): MockEmailCall {
		const matchingCalls = this.emailCalls.filter((call) => {
			if (criteria.to && call.to !== criteria.to) return false;

			if (criteria.subject) {
				const pattern =
					typeof criteria.subject === 'string'
						? new RegExp(criteria.subject, 'i')
						: criteria.subject;
				if (!pattern.test(call.subject)) return false;
			}

			if (criteria.containsText && call.text && !call.text.includes(criteria.containsText)) {
				return false;
			}

			if (criteria.containsHtml && call.html && !call.html.includes(criteria.containsHtml)) {
				return false;
			}

			return true;
		});

		if (matchingCalls.length === 0) {
			const criteriaStr = JSON.stringify(criteria, null, 2);
			const callsStr = JSON.stringify(this.emailCalls, null, 2);
			throw new Error(
				`No email found matching criteria:\n${criteriaStr}\n\nActual emails sent:\n${callsStr}`,
			);
		}

		return matchingCalls[0];
	}
}

/**
 * Global email mock service instance
 * Use this in your tests to access email mocking functionality
 */
export const emailMock = new EmailMockService();
